apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "php.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "php.labels" . | nindent 4 }}
data:
  # Environment
  SDBP_ENVIRONMENT: {{ .Values.environment | default "development" | quote }}

  # Database Configuration
  SDBP_DB_SERVER: {{ .Values.mysql.host | quote }}
  SDBP_DB_USER: {{ .Values.mysql.username | quote }}
  SDBP_DB_NAME: {{ .Values.mysql.database | quote }}
  SDBP_DB_PORT: {{ .Values.mysql.port | quote }}
  SDBP_DB_DEBUG: {{ .Values.environment.dbdebug | default | not | quote }}
  SDBP_DB_SOCKET: {{ .Values.mysql.socket | default "" | quote }}

  # Time and Date Settings
  SDBP_DEFAULT_TZ: {{ .Values.timezone | default "Europe/Moscow" | quote }}
  SDBP_DATE_FORMAT: {{ .Values.date.format | default "Y-m-d" | quote }}
  SDBP_DATE_PICKER_FORMAT: {{ .Values.date.pickerFormat | default "yyyy-mm-dd" | quote }}

  # Security Settings
  SDBP_LOGIN_LEN_MIN: {{ .Values.security.loginLenMin | default "3" | quote }}
  SDBP_LOGIN_LEN_MAX: {{ .Values.security.loginLenMax | default "255" | quote }}
  SDBP_LOGIN_TRIES_MAX: {{ .Values.security.loginTriesMax | default "3" | quote }}
  SDBP_LOGIN_TRIES_LOCK: {{ .Values.security.loginTriesLock | default "120" | quote }}
  SDBP_PSW_LEN_MIN: {{ .Values.security.pswLenMin | default "6" | quote }}
  SDBP_PSW_LEN_MAX: {{ .Values.security.pswLenMax | default "255" | quote }}
  SDBP_REMEMBER_ME_TIME: {{ .Values.security.rememberMeTime | default "2592000" | quote }}
  SDBP_PSW_LEN_MIN_CASH_REGISTER: {{ .Values.security.pswLenMinCashRegister | default "8" | quote }}
  SDBP_PSW_LEN_MAX_CASH_REGISTER: {{ .Values.security.pswLenMaxCashRegister | default "12" | quote }}

  # API Settings
  SDBP_GDS_API_HOST: {{ .Values.api.gdsHost | default "api.sdbp.local" | quote }}
  SDBP_API_TIME_LIMIT: {{ .Values.fpm.phpAdminValues.max_execution_time | quote }}
  SDBP_API_CONNECT_TIMEOUT: {{ .Values.api.connectTimeout | default "30" | quote }}
  SDBP_API_TIMEOUT: {{ .Values.api.timeout | default "90" | quote }}
  SDBP_API_MOBILE_DEFAULT_PHONE: {{ .Values.api.mobileDefaultPhone | default "+79999999999" | quote }}
  SDBP_API_MOBILE_DEFAULT_EMAIL: {{ .Values.api.mobileDefaultEmail | default "<EMAIL>" | quote }}
  SDBP_MAX_ROWS: {{ .Values.api.maxRows | default "1048576" | quote }}

  # Cache Settings
  SDBP_CACHE_CLASS: {{ .Values.cache.class | default "File" | quote }}
  SDBP_CACHE_DURATION: {{ .Values.cache.duration | default "+1 hours" | quote }}
  SDBP_CACHE_PREFIX: {{ printf "sdbp_%s" .Values.environment | quote }}
  SDBP_CACHE_PATH: {{ .Values.cache.path | default "cache/" | quote }}
  SDBP_CACHE_MASK: {{ .Values.cache.mask | default "0777" | quote }}

  # Redis Settings
  SDBP_REDIS_HOST: {{ .Values.redis.host | default "redis" | quote }}
  SDBP_REDIS_PORT: {{ .Values.redis.port | default "6379" | quote }}
  SDBP_REDIS_DATABASE: {{ .Values.redis.database | default "0" | quote }}
  SDBP_REDIS_PASSWORD: {{ .Values.redis.password | default "" | quote }}

  # API Cache Settings
  SDBP_API_CACHE_CONNECTION: {{ .Values.api.cache.connection | default "redis" | quote }}
  SDBP_API_CACHE_DEFAULT_CLASS: {{ .Values.api.cache.default.className | default "\\Cake\\Cache\\Engine\\FileEngine" | quote }}
  SDBP_API_CACHE_DEFAULT_DURATION: {{ .Values.api.cache.default.duration | default "+1 hours" | quote }}
  SDBP_API_CACHE_DEFAULT_PREFIX: {{ .Values.api.cache.default.prefix | default "sdbp_api" | quote }}
  SDBP_API_CACHE_DEFAULT_PATH: {{ .Values.api.cache.default.path | default "../cache/" | quote }}
  SDBP_API_CACHE_DEFAULT_MASK: {{ .Values.api.cache.default.mask | default "0777" | quote }}
  SDBP_API_CACHE_REDIS_CLASS: {{ .Values.api.cache.redis.className | default "\\Cake\\Cache\\Engine\\RedisEngine" | quote }}
  SDBP_API_CACHE_REDIS_TIMEOUT: {{ .Values.api.cache.redis.timeout | default "0" | quote }}
  SDBP_API_CACHE_REDIS_PERSISTENT: {{ .Values.api.cache.redis.persistent | default false | quote }}
  SDBP_API_CACHE_REDIS_DURATION: {{ .Values.api.cache.redis.duration | default "+1 hours" | quote }}
  SDBP_API_CACHE_REDIS_PREFIX: {{ .Values.api.cache.redis.prefix | default "sdbp-kube_api_" | quote }}



  # Admin Module Settings
  SDBP_ADMIN_PAYMENT_OWNER_SYSTEM_TITLE: {{ .Values.environment.title | default "" | quote }}
  SDBP_ADMIN_GDS_API_URL: {{ printf "http://%s" .Values.api.gdsHost | default "http://api.sdbp.local" | quote }}
  SDBP_ADMIN_KEYCLOAK_BASE_URL: {{ .Values.keycloak.baseUrl | default "http://keycloak:8080" | quote }}
  SDBP_ADMIN_KEYCLOAK_ENABLED: {{ .Values.keycloak.enabled | default false | quote }}
  SDBP_ADMIN_KEYCLOAK_CLIENT_ID: {{ .Values.keycloak.clientId | default "" | quote }}
  SDBP_ADMIN_KEYCLOAK_REALM: {{ .Values.keycloak.realm | default "" | quote }}
  SDBP_ADMIN_TELEGRAM_FAIL_SEND: {{ .Values.admin.telegram.failSend | default false | quote }}
  SDBP_ADMIN_API_MOBILE: {{ .Values.api.mobileUrl | default "http://am.sdbp.local" | quote }}
  SDBP_ADMIN_LKP_ENDPOINT: {{ .Values.admin.lkp.endpoint | default "localhost:5000" | quote }}
  SDBP_ADMIN_LKP_ENABLED: {{ .Values.admin.lkp.enabled | default false | quote }}
  SDBP_ADMIN_LKP_LOG_LEVEL: {{ .Values.admin.lkp.logLevel | default "2" | quote }}
  SDBP_ADMIN_LKP_SSL: {{ .Values.admin.lkp.ssl | default true | quote }}
  SDBP_ADMIN_LKP_UPDATE_STATUS: {{ .Values.admin.lkp.updateStatus | default "60000" | quote }}
  SDBP_ADMIN_REGION_ID: {{ .Values.admin.regionId | default "0" | quote }}
  SDBP_ADMIN_MAP_PROVIDER: {{ .Values.admin.mapProvider | default "osm" | quote }}
  SDBP_ADMIN_LANGUAGE: {{ .Values.admin.language | default "ru" | quote }}

  # API Module Settings
  SDBP_API_OUTER_API_LOG: {{ .Values.api.outerApiLog | default true | quote }}
  SDBP_API_OUTER_API_LOG_SQL_CHUNK_SIZE: {{ .Values.api.outerApiLogSqlChunkSize | default "1" | quote }}
  SDBP_API_BUY_TICKET_CNT_MAX: {{ .Values.api.buyTicketCntMax | default "5" | quote }}
  SDBP_API_TELEGRAM_FAIL_SEND: {{ .Values.api.telegram.failSend | default true | quote }}
  SDBP_API_AUTO_REFUND: {{ .Values.api.autoRefund | default "0" | quote }}
  SDBP_API_DEFAULT_AGENT_ID: {{ .Values.api.defaultAgentId | default "1" | quote }}
  SDBP_API_NSI_HTTP_TIMEOUT: {{ .Values.api.nsiHttpTimeout | default "30" | quote }}
  SDBP_API_TARIFICATION_TIMEOUT: {{ .Values.api.tarification.timeout | default "0" | quote }}
  SDBP_API_TARIFICATION_DAYS_EXPIRED: {{ .Values.api.tarification.daysExpired | default "2" | quote }}
  SDBP_API_TARIFICATION_TRANSFER_AVAILABLE_COUNT: {{ .Values.api.tarification.transferAvailableCount | default "" | quote }}
  SDBP_API_EXPIRATION_DATE_SHIFT_TEMPLATE: {{ .Values.api.expirationDateShiftTemplate | default "0" | quote }}
  SDBP_API_TRANSFER_LEFTOVERS: {{ .Values.api.transferLeftovers | default false | quote }}
  SDBP_API_AUTO_SET_USER_ID: {{ .Values.api.autoSetUserID | default false | quote }}
  SDBP_API_CHECK_TIMEZONE_OFF: {{ .Values.api.checkTimezoneOff | default false | quote }}
  SDBP_API_MAX_TT_REQUEST_COUNT: {{ .Values.api.maxTtRequestCount | default "1000" | quote }}

  # API GoldCrown FTP Settings
  SDBP_API_GOLDCROWN_HOST: {{ .Values.api.goldCrown.host | default "localhost" | quote }}
  SDBP_API_GOLDCROWN_PORT: {{ .Values.api.goldCrown.port | default "21" | quote }}
  SDBP_API_GOLDCROWN_USERNAME: {{ .Values.api.goldCrown.username | default "foo" | quote }}
  SDBP_API_GOLDCROWN_PASSWORD: {{ .Values.api.goldCrown.password | default "pass" | quote }}
  SDBP_API_GOLDCROWN_ROOT_FOLDER: {{ .Values.api.goldCrown.rootFolder | default "/" | quote }}
  SDBP_API_GOLDCROWN_SOURCE_FOLDER: {{ .Values.api.goldCrown.sourceFolder | default "/Korona" | quote }}
  SDBP_API_GOLDCROWN_ANSWER_FOLDER: {{ .Values.api.goldCrown.answerFolder | default "/Troika" | quote }}
  SDBP_API_GOLDCROWN_OUTPUT_FOLDER: {{ .Values.api.goldCrown.outputFolder | default "/Troika" | quote }}

  # API Keycloak Settings
  SDBP_API_KEYCLOAK_CLIENT_ID: {{ .Values.api.keycloak.clientId | default "some_client_id" | quote }}
  SDBP_API_KEYCLOAK_CLIENT_SECRET: {{ .Values.api.keycloak.clientSecret | default "secret" | quote }}
  SDBP_API_KEYCLOAK_REALM: {{ .Values.api.keycloak.realm | default "some_realm" | quote }}
  SDBP_API_KEYCLOAK_BASE_URL: {{ .Values.api.keycloak.baseUrl | default "http://keycloak:8080" | quote }}
  SDBP_API_KEYCLOAK_ENABLED: {{ .Values.api.keycloak.enabled | default true | quote }}

  # Logging Settings
  SDBP_LOG_SUCCESS: {{ .Values.logging.success | default true | quote }}
  SDBP_LOG_ERROR: {{ .Values.logging.error | default true | quote }}
  SDBP_LOG_METHODS: {{ .Values.logging.methods | default "POST,GET,PUT,DELETE" | quote }}
  SDBP_LOG_ANSWER: {{ .Values.logging.answer | default true | quote }}
  SDBP_LOG_SERVER: {{ .Values.logging.server | default true | quote }}
  SDBP_LOG_GET: {{ .Values.logging.get | default true | quote }}
  SDBP_LOG_POST: {{ .Values.logging.post | default true | quote }}
  SDBP_LOG_FILES: {{ .Values.logging.files | default true | quote }}
  SDBP_LOG_SQL: {{ .Values.logging.sql | default true | quote }}

  # Directory Settings
  SDBP_DIR_CASH_REGISTER_CFG: {{ .Values.directories.cashRegisterCfg | default "../config/cash_register/" | quote }}

  # City Settings
  SDBP_EXCLUDE_CITIES_ON_NAME_FROM_LIST: {{ .Values.city.excludeCitiesOnNameFromList | default "[]" | quote }}
  SDBP_SHOW_ALL_CITIES_ON_YMAP: {{ .Values.city.showAllCitiesOnYMap | default "false" | quote }}

  # API-Mobile Module Settings
  SDBP_API_MOBILE_CACHE_CLASS: {{ .Values.apiMobile.cache.className | default "File" | quote }}
  SDBP_API_MOBILE_CACHE_DURATION: {{ .Values.apiMobile.cache.duration | default "+1 hours" | quote }}
  SDBP_API_MOBILE_CACHE_PREFIX: {{ .Values.apiMobile.cache.prefix | default "sdbp_api_mobile" | quote }}
  SDBP_API_MOBILE_CACHE_PATH: {{ .Values.apiMobile.cache.path | default "../cache/" | quote }}
  SDBP_API_MOBILE_CACHE_MASK: {{ .Values.apiMobile.cache.mask | default "0777" | quote }}
  SDBP_API_MOBILE_OUTER_API_LOG: {{ .Values.apiMobile.outerApiLog | default true | quote }}
  SDBP_API_MOBILE_OUTER_API_LOG_SQL_CHUNK_SIZE: {{ .Values.apiMobile.outerApiLogSqlChunkSize | default "1" | quote }}
  SDBP_API_MOBILE_BUY_TICKET_CNT_MAX: {{ .Values.apiMobile.buyTicketCntMax | default "5" | quote }}
  SDBP_API_MOBILE_MAX_TERMINAL_LOG_COUNT: {{ .Values.apiMobile.maxTerminalLogCount | default "5" | quote }}
  SDBP_API_MOBILE_TELEGRAM_FAIL_SEND: {{ .Values.apiMobile.telegram.failSend | default true | quote }}
  SDBP_API_MOBILE_AUTO_SET_USER_ID: {{ .Values.apiMobile.autoSetUserID | default false | quote }}
  SDBP_API_MOBILE_CHECK_TIMEZONE_OFF: {{ .Values.apiMobile.checkTimezoneOff | default false | quote }}
  SDBP_API_MOBILE_URL: {{ .Values.apiMobile.url | default "http://api-mobile.sdbp.local" | quote }}

  php-fpm.conf: |
    [global]
    error_log = /proc/self/fd/2
    log_level = debug

    [www]
    access.log = /proc/self/fd/2
    access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

    listen = 9000

    pm = dynamic
    pm.max_children = {{ .Values.fpm.maxChildren }}
    pm.start_servers = {{ .Values.fpm.startServers }}
    pm.min_spare_servers = {{ .Values.fpm.minSpareServers }}
    pm.max_spare_servers = {{ .Values.fpm.maxSpareServers }}
    pm.max_requests = {{ .Values.fpm.maxRequests }}

    request_terminate_timeout = {{ .Values.fpm.requestTerminateTimeout }}
    request_slowlog_timeout = {{ .Values.fpm.requestSlowlogTimeout }}
    slowlog = /proc/self/fd/2

    catch_workers_output = yes
    decorate_workers_output = no

    clear_env = no

    php_admin_value[error_log] = /proc/self/fd/2
    php_admin_flag[log_errors] = on

  php.ini: |
    [PHP]

    ;;;;;;;;;;;;;;;;;;;
    ; About php.ini   ;
    ;;;;;;;;;;;;;;;;;;;
    ; PHP's initialization file, generally called php.ini, is responsible for
    ; configuring many of the aspects of PHP's behavior.

    ; PHP attempts to find and load this configuration from a number of locations.
    ; The following is a summary of its search order:
    ; 1. SAPI module specific location.
    ; 2. The PHPRC environment variable. (As of PHP 5.2.0)
    ; 3. A number of predefined registry keys on Windows (As of PHP 5.2.0)
    ; 4. Current working directory (except CLI)
    ; 5. The web server's directory (for SAPI modules), or directory of PHP
    ; (otherwise in Windows)
    ; 6. The directory from the --with-config-file-path compile time option, or the
    ; Windows directory (usually C:\windows)
    ; See the PHP docs for more specific information.
    ; http://php.net/configuration.file

    ; The syntax of the file is extremely simple.  Whitespace and lines
    ; beginning with a semicolon are silently ignored (as you probably guessed).
    ; Section headers (e.g. [Foo]) are also silently ignored, even though
    ; they might mean something in the future.

    ; Directives following the section heading [PATH=/www/mysite] only
    ; apply to PHP files in the /www/mysite directory.  Directives
    ; following the section heading [HOST=www.example.com] only apply to
    ; PHP files served from www.example.com.  Directives set in these
    ; special sections cannot be overridden by user-defined INI files or
    ; at runtime. Currently, [PATH=] and [HOST=] sections only work under
    ; CGI/FastCGI.
    ; http://php.net/ini.sections

    ; Directives are specified using the following syntax:
    ; directive = value
    ; Directive names are *case sensitive* - foo=bar is different from FOO=bar.
    ; Directives are variables used to configure PHP or PHP extensions.
    ; There is no name validation.  If PHP can't find an expected
    ; directive because it is not set or is mistyped, a default value will be used.

    ; The value can be a string, a number, a PHP constant (e.g. E_ALL or M_PI), one
    ; of the INI constants (On, Off, True, False, Yes, No and None) or an expression
    ; (e.g. E_ALL & ~E_NOTICE), a quoted string ("bar"), or a reference to a
    ; previously set variable or directive (e.g. ${foo})

    ; Expressions in the INI file are limited to bitwise operators and parentheses:
    ; |  bitwise OR
    ; ^  bitwise XOR
    ; &  bitwise AND
    ; ~  bitwise NOT
    ; !  boolean NOT

    ; Boolean flags can be turned on using the values 1, On, True or Yes.
    ; They can be turned off using the values 0, Off, False or No.

    ; An empty string can be denoted by simply not writing anything after the equal
    ; sign, or by using the None keyword:

    ; foo =         ; sets foo to an empty string
    ; foo = None    ; sets foo to an empty string
    ; foo = "None"  ; sets foo to the string 'None'

    ; If you use constants in your value, and these constants belong to a
    ; dynamically loaded extension (either a PHP extension or a Zend extension),
    ; you may only use these constants *after* the line that loads the extension.

    ;;;;;;;;;;;;;;;;;;;
    ; About this file ;
    ;;;;;;;;;;;;;;;;;;;
    ; PHP comes packaged with two INI files. One that is recommended to be used
    ; in production environments and one that is recommended to be used in
    ; development environments.

    ; php.ini-production contains settings which hold security, performance and
    ; best practices at its core. But please be aware, these settings may break
    ; compatibility with older or less security conscience applications. We
    ; recommending using the production ini in production and testing environments.

    ; php.ini-development is very similar to its production variant, except it is
    ; much more verbose when it comes to errors. We recommend using the
    ; development version only in development environments, as errors shown to
    ; application users can inadvertently leak otherwise secure information.

    ; This is the php.ini-production INI file.

    ;;;;;;;;;;;;;;;;;;;
    ; Quick Reference ;
    ;;;;;;;;;;;;;;;;;;;
    ; The following are all the settings which are different in either the production
    ; or development versions of the INIs with respect to PHP's default behavior.
    ; Please see the actual settings later in the document for more details as to why
    ; we recommend these changes in PHP's behavior.

    ; display_errors
    ;   Default Value: On
    ;   Development Value: On
    ;   Production Value: Off

    ; display_startup_errors
    ;   Default Value: Off
    ;   Development Value: On
    ;   Production Value: Off

    ; error_reporting
    ;   Default Value: E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED
    ;   Development Value: E_ALL
    ;   Production Value: E_ALL & ~E_DEPRECATED & ~E_STRICT

    ; log_errors
    ;   Default Value: Off
    ;   Development Value: On
    ;   Production Value: On
    log_errors = On

    ; max_input_time
    ;   Default Value: -1 (Unlimited)
    ;   Development Value: 60 (60 seconds)
    ;   Production Value: 60 (60 seconds)

    ; output_buffering
    ;   Default Value: Off
    ;   Development Value: 4096
    ;   Production Value: 4096

    ; register_argc_argv
    ;   Default Value: On
    ;   Development Value: Off
    ;   Production Value: Off

    ; request_order
    ;   Default Value: None
    ;   Development Value: "GP"
    ;   Production Value: "GP"

    ; session.gc_divisor
    ;   Default Value: 100
    ;   Development Value: 1000
    ;   Production Value: 1000

    ; session.sid_bits_per_character
    ;   Default Value: 4
    ;   Development Value: 5
    ;   Production Value: 5

    ; short_open_tag
    ;   Default Value: On
    ;   Development Value: Off
    ;   Production Value: Off

    ; variables_order
    ;   Default Value: "EGPCS"
    ;   Development Value: "GPCS"
    ;   Production Value: "GPCS"

    ;;;;;;;;;;;;;;;;;;;;
    ; php.ini Options  ;
    ;;;;;;;;;;;;;;;;;;;;
    ; Name for user-defined php.ini (.htaccess) files. Default is ".user.ini"
    ;user_ini.filename = ".user.ini"

    ; To disable this feature set this option to an empty value
    ;user_ini.filename =

    ; TTL for user-defined php.ini files (time-to-live) in seconds. Default is 300 seconds (5 minutes)
    ;user_ini.cache_ttl = 300

    ;;;;;;;;;;;;;;;;;;;;
    ; Language Options ;
    ;;;;;;;;;;;;;;;;;;;;

    ; Enable the PHP scripting language engine under Apache.
    ; http://php.net/engine
    engine = On

    ; This directive determines whether or not PHP will recognize code between
    ; <? and ?> tags as PHP source which should be processed as such. It is
    ; generally recommended that <?php and ?> should be used and that this feature
    ; should be disabled, as enabling it may result in issues when generating XML
    ; documents, however this remains supported for backward compatibility reasons.
    ; Note that this directive does not control the <?= shorthand tag, which can be
    ; used regardless of this directive.
    ; Default Value: On
    ; Development Value: Off
    ; Production Value: Off
    ; http://php.net/short-open-tag
    short_open_tag = On

    ; The number of significant digits displayed in floating point numbers.
    ; http://php.net/precision
    precision = 14

    ; Output buffering is a mechanism for controlling how much output data
    ; (excluding headers and cookies) PHP should keep internally before pushing that
    ; data to the client. If your application's output exceeds this setting, PHP
    ; will send that data in chunks of roughly the size you specify.
    ; Turning on this setting and managing its maximum buffer size can yield some
    ; interesting side-effects depending on your application and web server.
    ; You may be able to send headers and cookies after you've already sent output
    ; through print or echo. You also may see performance benefits if your server is
    ; emitting less packets due to buffered output versus PHP streaming the output
    ; as it gets it. On production servers, 4096 bytes is a good setting for performance
    ; reasons.
    ; Note: Output buffering can also be controlled via Output Buffering Control
    ;   functions.
    ; Possible Values:
    ;   On = Enabled and buffer is unlimited. (Use with caution)
    ;   Off = Disabled
    ;   Integer = Enables the buffer and sets its maximum size in bytes.
    ; Note: This directive is hardcoded to Off for the CLI SAPI
    ; Default Value: Off
    ; Development Value: 4096
    ; Production Value: 4096
    ; http://php.net/output-buffering
    output_buffering = 4096

    ; Transparent output compression using the zlib library
    ; Valid values for this option are 'off', 'on', or a specific buffer size
    ; to be used for compression (default is 4KB)
    ; Note: Resulting chunk size may vary due to nature of compression. PHP
    ;   outputs chunks that are few hundreds bytes each as a result of
    ;   compression. If you prefer a larger chunk size for better
    ;   performance, enable output_buffering in addition.
    ; Note: You need to use zlib.output_handler instead of the standard
    ;   output_handler, or otherwise the output will be corrupted.
    ; http://php.net/zlib.output-compression
    zlib.output_compression = Off

    ; Implicit flush tells PHP to tell the output layer to flush itself
    ; automatically after every output block.  This is equivalent to calling the
    ; PHP function flush() after each and every call to print() or echo() and each
    ; and every HTML block.  Turning this option on has serious performance
    ; implications and is generally recommended for debugging purposes only.
    ; http://php.net/implicit-flush
    ; Note: This directive is hardcoded to On for the CLI SAPI
    implicit_flush = Off

    ; The unserialize callback function will be called (with the undefined class'
    ; name as parameter), if the unserializer finds an undefined class
    ; which should be instantiated. A warning appears if the specified function is
    ; not defined, or if the function doesn't include/implement the missing class.
    ; So only set this entry, if you really want to implement such a
    ; callback-function.
    unserialize_callback_func =

    ; When floats & doubles are serialized, store serialize_precision significant
    ; digits after the floating point. The default value ensures that when floats
    ; are decoded with unserialize, the data will remain the same.
    ; The value is also used for json_encode when encoding double values.
    ; If -1 is used, then dtoa mode 0 is used which automatically select the best
    ; precision.
    serialize_precision = -1

    ; This directive allows you to disable certain functions.
    ; It receives a comma-delimited list of function names.
    ; http://php.net/disable-functions
    disable_functions = pcntl_alarm,pcntl_waitpid,pcntl_wait,pcntl_wifexited,pcntl_wifstopped,pcntl_wifsignaled,pcntl_wifcontinued,pcntl_wexitstatus,pcntl_wtermsig,pcntl_wstopsig,pcntl_signal,pcntl_signal_get_handler,pcntl_signal_dispatch,pcntl_get_last_error,pcntl_strerror,pcntl_sigprocmask,pcntl_sigwaitinfo,pcntl_sigtimedwait,pcntl_exec,pcntl_getpriority,pcntl_setpriority,pcntl_async_signals,pcntl_unshare,

    ; This directive allows you to disable certain classes.
    ; It receives a comma-delimited list of class names.
    ; http://php.net/disable-classes
    disable_classes =

    ; Enables or disables the circular reference collector.
    ; http://php.net/zend.enable-gc
    zend.enable_gc = On

    ; Allows to include or exclude arguments from stack traces generated for exceptions.
    ; In production, it is recommended to turn this setting on to prohibit the output
    ; of sensitive information in stack traces
    ; Default: Off
    zend.exception_ignore_args = On

    ; Decides whether PHP may expose the fact that it is installed on the server
    ; (e.g. by adding its signature to the Web server header).  It is no security
    ; threat in any way, but it makes it possible to determine whether you use PHP
    ; on your server or not.
    ; http://php.net/expose-php
    expose_php = Off

    ;;;;;;;;;;;;;;;;;;;
    ; Resource Limits ;
    ;;;;;;;;;;;;;;;;;;;

    ; Maximum execution time of each script, in seconds
    ; http://php.net/max-execution-time
    ; Note: This directive is hardcoded to 0 for the CLI SAPI
    max_execution_time = {{ .Values.fpm.phpAdminValues.max_execution_time }}

    ; Maximum amount of time each script may spend parsing request data. It's a good
    ; idea to limit this time on productions servers in order to eliminate unexpectedly
    ; long running scripts.
    ; Note: This directive is hardcoded to -1 for the CLI SAPI
    ; Default Value: -1 (Unlimited)
    ; Development Value: 60 (60 seconds)
    ; Production Value: 60 (60 seconds)
    ; http://php.net/max-input-time
    max_input_time = 60

    ; Maximum amount of memory a script may consume
    ; http://php.net/memory-limit
    memory_limit = {{ .Values.fpm.phpAdminValues.memory_limit }}

    ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
    ; Error handling and logging ;
    ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

    ; This directive informs PHP of which errors, warnings and notices you would like
    ; it to take action for. The recommended way of setting values for this
    ; directive is through the use of the error level constants and bitwise
    ; operators. The error level constants are below here for convenience as well as
    ; some common settings and their meanings.
    ; By default, PHP is set to take action on all errors, notices and warnings EXCEPT
    ; those related to E_NOTICE and E_STRICT, which together cover best practices and
    ; recommended coding standards in PHP. For performance reasons, this is the
    ; recommend error reporting setting. Your production server shouldn't be wasting
    ; resources complaining about best practices and coding standards. That's what
    ; development servers and development settings are for.
    ; Note: The php.ini-development file has this setting as E_ALL. This
    ; means it pretty much reports everything which is exactly what you want during
    ; development and early testing.
    ;
    ; Error Level Constants:
    ; E_ALL             - All errors and warnings (includes E_STRICT as of PHP 5.4.0)
    ; E_ERROR           - fatal run-time errors
    ; E_RECOVERABLE_ERROR  - almost fatal run-time errors
    ; E_WARNING         - run-time warnings (non-fatal errors)
    ; E_PARSE           - compile-time parse errors
    ; E_NOTICE          - run-time notices (these are warnings which often result
    ;                     from a bug in your code, but it's possible that it was
    ;                     intentional (e.g., using an uninitialized variable and
    ;                     relying on the fact it is automatically initialized to an
    ;                     empty string)
    ; E_STRICT          - run-time notices, enable to have PHP suggest changes
    ;                     to your code which will ensure the best interoperability
    ;                     and forward compatibility of your code
    ; E_CORE_ERROR      - fatal errors that occur during PHP's initial startup
    ; E_CORE_WARNING    - warnings (non-fatal errors) that occur during PHP's
    ;                     initial startup
    ; E_COMPILE_ERROR   - fatal compile-time errors
    ; E_COMPILE_WARNING - compile-time warnings (non-fatal errors)
    ; E_USER_ERROR      - user-generated error message
    ; E_USER_WARNING    - user-generated warning message
    ; E_USER_NOTICE     - user-generated notice message
    ; E_DEPRECATED      - warn about code that will not work in future versions
    ;                     of PHP
    ; E_USER_DEPRECATED - user-generated deprecation warnings
    ;
    ; Common Values:
    ;   E_ALL (Show all errors, warnings and notices including coding standards.)
    ;   E_ALL & ~E_NOTICE  (Show all errors, except for notices)
    ;   E_ALL & ~E_NOTICE & ~E_STRICT  (Show all errors, except for notices and coding standards warnings.)
    ;   E_COMPILE_ERROR|E_RECOVERABLE_ERROR|E_ERROR|E_CORE_ERROR  (Show only errors)
    ; Default Value: E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED
    ; Development Value: E_ALL
    ; Production Value: E_ALL & ~E_DEPRECATED & ~E_STRICT
    ; http://php.net/error-reporting
    error_reporting = E_ALL

    ; This directive controls whether or not and where PHP will output errors,
    ; notices and warnings too. Error output is very useful during development, but
    ; it could be very dangerous in production environments. Depending on the code
    ; which is triggering the error, sensitive information could potentially leak
    ; out of your application such as database usernames and passwords or worse.
    ; For production environments, we recommend logging errors rather than
    ; sending them to STDOUT.
    ; Possible Values:
    ;   Off = Do not display any errors
    ;   stderr = Display errors to STDERR (affects only CGI/CLI binaries!)
    ;   On or stdout = Display errors to STDOUT
    ; Default Value: On
    ; Development Value: On
    ; Production Value: Off
    ; http://php.net/display-errors
    display_errors = Off

    ; The display of errors which occur during PHP's startup sequence are handled
    ; separately from display_errors. PHP's default behavior is to suppress those
    ; errors from clients. Turning the display of startup errors on can be useful in
    ; debugging configuration problems. We strongly recommend you
    ; set this to 'off' for production servers.
    ; Default Value: Off
    ; Development Value: On
    ; Production Value: Off
    ; http://php.net/display-startup-errors
    display_startup_errors = Off

    ; Besides displaying errors, PHP can also log errors to locations such as a
    ; server-specific log, STDERR, or a location specified by the error_log
    ; directive found below. While errors should not be displayed on productions
    ; servers they should still be monitored and logging is a great way to do that.
    ; Default Value: Off
    ; Development Value: On
    ; Production Value: On
    ; http://php.net/log-errors
    log_errors = On

    ; Set maximum length of log_errors. In error_log information about the source is
    ; added. The default is 1024 and 0 allows to not apply any maximum length at all.
    ; http://php.net/log-errors-max-len
    log_errors_max_len = 1024

    ; Do not log repeated messages. Repeated errors must occur in same file on same
    ; line unless ignore_repeated_source is set true.
    ; http://php.net/ignore-repeated-errors
    ignore_repeated_errors = Off

    ; Ignore source of message when ignoring repeated messages. When this setting
    ; is On you will not log errors with repeated messages from different files or
    ; source lines.
    ; http://php.net/ignore-repeated-source
    ignore_repeated_source = Off

    ; If this parameter is set to Off, then memory leaks will not be shown (on
    ; stdout or in the log). This is only effective in a debug compile, and if
    ; error reporting includes E_WARNING in the allowed list
    ; http://php.net/report-memleaks
    report_memleaks = On

    ; Log errors to specified file. PHP's default behavior is to leave this value
    ; empty.
    ; http://php.net/error-log
    ; Example:
    ;error_log = php_errors.log
    ; Log errors to syslog (Event Log on Windows).
    ;error_log = syslog
    error_log = /proc/self/fd/2
